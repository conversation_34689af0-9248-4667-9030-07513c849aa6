<style lang="less">
@import '../../common/common.less';
.list-item {
  display: flex;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx 1rpx rgba(3, 3, 3, 0.06);
  border-radius: 20rpx;
  .left {
    width: 200rpx;
    height: 200rpx;
    border-radius: 20rpx;
  }
  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 20rpx;
    .name {
      font-size: 32rpx;
      color: #393737;
      font-weight: bold;
      .ellipsis(2);
    }
    .bottom {
      .vip {
        .flex(center, start);
        flex-wrap: wrap;
        .price {
          font-size: 28rpx;
          font-weight: bold;
          color: #129799;
          margin-right: 10rpx;
        }
        .tag {
          line-height: 36rpx;
          text-align: center;
          background: #a8f2ed;
          padding: 0 10rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          color: #052829;
        }
      }
      .normal {
        .flex(center, start);
        flex-wrap: wrap;
        .price {
          font-size: 28rpx;
          text-decoration: line-through;
          margin-right: 10rpx;
        }
        .tag {
          line-height: 34rpx;
          text-align: center;
          border: 1rpx solid #afafaf;
          padding: 0 10rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
          box-sizing: border-box;
        }
      }
      .earnest {
        .flex(center, center);
        .tag {
          font-size: 20rpx;
          padding: 0 10rpx;
          line-height: 35rpx;
          text-align: center;
          border-radius: 8rpx;
          color: #fff;
          background-color: #428485;
        }
        .price {
          font-size: 28rpx;
          font-weight: bold;
          color: #242d2b;
          margin-right: 10rpx;
        }
      }
      .price-row1 {
        .flex(center,space-between);
        margin-top: 15rpx;
      }
      .price-row2 {
        .flex(center,space-between);
        margin-top: 15rpx;
        .cart2 {
          padding: 10rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20rpx;
          font-weight: bold;
          color: #fff;
          .btn1-bg();
          image {
            width: 35rpx;
            height: 35rpx;
            margin-right: 10rpx;
          }
        }
      }
      &:not(:last-child) {
        padding-bottom: 30rpx;
        border-bottom: 1rpx solid #eee;
      }
    }
  }
}
.small-list-item {
  padding: 20rpx;
  .left {
    width: 160rpx;
    height: 160rpx;
    border-radius: 10rpx;
  }
  .right {
    .name {
      font-size: 28rpx;
      font-weight: 400;
      .tag {
        font-size: 18rpx;
      }
    }
    .vip {
      .tag {
        font-size: 16rpx;
        padding: 2rpx 10rpx;
        line-height: 30rpx;
        max-width: 130rpx;
      }
      .price {
        font-size: 24rpx;
        flex: 1;
      }
    }
    .normal {
      .flex(center, center);
      .tag {
        font-size: 16rpx;
        padding: 0 10rpx;
        line-height: 30rpx;
      }
      .price {
        font-size: 24rpx;
      }
    }
    .earnest {
      .tag {
        font-size: 19rpx;
        padding: 0 10rpx;
        line-height: 35rpx;
      }
      .price {
        font-size: 28rpx;
      }
    }
    .price-row2 {
      .cart {
        width: 47rpx;
        height: 47rpx;
      }
    }
  }
}
.item-hover {
  background-color: #eee;
}
</style>
<template>
  <div
    :class="{ 'list-item': true, 'small-list-item': isSmall }"
    style="{{ customStyle }}"
    hover-class="{{ hover ? 'item-hover' : '' }}"
  >
    <img :src="data.Image" alt="" class="left" mode="aspecFill" />
    <div class="right">
      <div class="name">{{ data.Name }}</div>
      <div class="bottom">
        <div class="price-row1">
          <div class="normal">
            <div class="price">¥{{ data.RetailPrice }}</div>
            <span class="tag">市场价</span>
          </div>
        </div>
        <div class="price-row1" v-if="isDeposit">
          <div class="vip">
            <div class="price">
              {{ data.PointProduct === 'True' ? '' : '¥' }}<span>{{ data.MemberPrice }}</span>
            </div>
            <span class="tag">{{ data.PointProduct === 'True' ? '积分' : '会员价' }}</span>
          </div>
        </div>
        <div class="price-row2">
          <div class="earnest" v-if="isDeposit">
            <div class="price">¥{{ data.Deposit }}</div>
            <span class="tag">服务确认金</span>
          </div>
          <div class="vip" v-if="!isDeposit">
            <div class="price">
              {{ data.PointProduct === 'True' ? '' : '¥' }}<span>{{ data.MemberPrice }}</span>
            </div>
            <span class="tag">{{ data.PointProduct === 'True' ? '积分' : '会员价' }}</span>
          </div>
          <div v-else></div>
          <img src="/static/home/<USER>" class="cart" v-if="isSmall" />
          <div class="cart2" v-else>
            <img src="/static/home/<USER>" alt="" />
            加入购物车
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import wepy from '@wepy/core'

wepy.component({
  props: {
    data: Object,
    customStyle: String,
    hover: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'default'
    }
  },
  computed: {
    isSmall() {
      return this.size === 'small'
    },
    isDeposit() {
      const { data } = this
      return data && data.Product === 'False' && data.FeaturedProduct === 'False'
    }
  }
})
</script>
