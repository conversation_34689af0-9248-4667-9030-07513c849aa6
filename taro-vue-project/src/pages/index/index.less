/* 首页样式文件 - 从WePY项目迁移到Taro */
/* 使用rpx单位保持与原项目一致的显示效果 */

/* 引入通用样式 */
@import '../../common/common.less';

/* 页面基础样式 */
page {
  background-color: #f6f6f6;
}

.container {
  background-color: #f6f6f6;
  min-height: 100vh;
}

/* 骨架屏样式 */
.skeleton {
  .skeleton-header {
    height: 200rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  .skeleton-banner {
    height: 280rpx;
    margin: 28rpx 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8rpx;
  }

  .skeleton-menu {
    height: 300rpx;
    margin: 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 20rpx;
  }

  .skeleton-content {
    height: 600rpx;
    margin: 20rpx;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 15rpx;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 按钮样式 - 从原项目迁移 */
.btn1-bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #baf8e5, #8ef4d4);
  label {
    font-size: 18rpx;
    color: #050505;
    font-weight: bold;
  }
}

.btn2-bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #e6e6e6, #d1d1d1);
  label {
    font-size: 18rpx;
    color: #073a38;
    font-weight: bold;
  }
}

.btn3-bg {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background: linear-gradient(to bottom, #36aaba, #30626a);
  label {
    font-size: 18rpx;
    color: #fff;
    font-weight: bold;
  }
}

/* 输入框样式 */
.name-input {
  margin: 50rpx 30rpx;
  text-align: center;
}

/* 红包样式 - 从原项目迁移 */
.redpacket {
  position: fixed;
  right: 35rpx;
  bottom: 100rpx;
  z-index: 9;
  width: 295rpx;
  height: 220rpx;
  .line {
    position: absolute;
    left: 144rpx;
    top: -550rpx;
    width: 5rpx;
    height: 550rpx;
    background-color: #ce0211;
  }
  .con {
    position: relative;
    width: 100%;
    height: 100%;
    .box {
      width: 100%;
      height: 100%;
    }
    .close {
      position: absolute;
      right: -25rpx;
      top: -25rpx;
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      image {
        width: 100%;
        height: 100%;
        width: 42rpx;
        height: 42rpx;
      }
    }
  }
}

.redpacket-swiper {
  width: 750rpx;
  .con {
    width: 100%;
    height: 450rpx;
    .item {
      position: relative;
      width: 100%;
      height: 375rpx;
      image {
        position: absolute;
        left: 50%;
        top: 0;
        margin-left: -285rpx;
        width: 570rpx;
        height: 100%;
      }
      .price {
        position: absolute;
        left: 0%;
        top: 150rpx;
        width: 100%;
        text-align: center;
        color: #ffe77a;
        font-weight: 900;
        z-index: 99;
        font-size: 80rpx;
        .unit {
          font-size: 50rpx;
        }
      }
    }
  }
  .close {
    display: block;
    width: 60rpx;
    height: 60rpx;
    margin: 55rpx auto 0;
  }
  .wx-swiper-dot.wx-swiper-dot-active {
    width: 30rpx;
    border-radius: 15rpx;
  }
}

/* 自定义头部样式 - 从原项目迁移 */
.custom-header {
  position: sticky;
  top: 0;
  background-color: @primary-color;
  z-index: 10;
  .header-title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
    image {
      position: absolute;
      left: 55rpx;
      top: 0;
      width: 90rpx;
      height: 90rpx;
    }
    .shop-name {
      text-align: center;
      .name1 {
        font-size: 25rpx;
        color: #0e5453;
        font-weight: bold;
      }
      .name2 {
        font-size: 20rpx;
        color: #0e5453;
      }
    }
  }
  .header {
    .flex();
    padding: 0 24rpx;
    width: 100%;
    height: 88rpx;
    box-sizing: border-box;
    z-index: 10;
    .left {
      .flex(center,center);
      .icon1 {
        width: 35rpx;
        height: 35rpx;
      }
      view {
        font-size: 30rpx;
        padding: 0 8rpx;
        color: #0f5760;
      }
      .icon2 {
        width: 0;
        height: 0;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-top: 20rpx solid #0e5453;
        margin-left: 8rpx;
      }
    }
    .mid {
      .flex(center, space-between);
      flex: 1;
      height: 53rpx;
      background: #ffffff;
      border-radius: 24rpx;
      margin: 0 10rpx;
      padding: 0 10rpx;
      .icon {
        font-size: 34rpx;
        color: #0e5453;
      }
      text {
        font-size: 25rpx;
        color: #b3b3b3;
      }
    }
    .right {
      .flex(center, center);
      color: #0f5760;
      font-size: 28rpx;
      image {
        width: 30rpx;
        height: 30rpx;
      }
      view {
        line-height: 1.2;
        .icon {
          width: 0;
          height: 0;
          border-top: 10rpx solid transparent;
          border-bottom: 10rpx solid transparent;
          border-right: 20rpx solid #0e5453;
          margin-left: 8rpx;
        }
      }
    }
  }
}

/* 主要内容区域 */
.main {
  padding: 0 20rpx;
  .swiper {
    margin-top: 28rpx;
    height: 280rpx;
    .swiper-item {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }
  }
  .tips {
    margin: 30rpx 0;
    background-color: #fff;
    border-radius: 6rpx;
    font-size: 32rpx;
    color: #1c1c1d;
    padding: 9rpx 20rpx;
    line-height: 48rpx;
    text-align: center;
    .ellipsis();
  }
  .tabs {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 20rpx 0;
    background-color: #fff;
    padding: 40rpx 40rpx 0;
    box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
    border-radius: 20rpx;
    .tab-item {
      margin-bottom: 40rpx;
      &:not(:nth-child(4n)) {
        margin-right: 34rpx;
      }
      image {
        width: 130rpx;
        height: 125rpx;
      }
      view {
        width: 130rpx;
        font-size: 28rpx;
        color: #5b5a5a;
        text-align: center;
        .ellipsis();
      }
    }
  }

  .box {
    margin-top: 20rpx;
    .head {
      .flex(center,center);
      height: 66rpx;
      background: #c6fced;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;
      image {
        width: 150rpx;
        height: 35rpx;
      }
      text {
        font-size: 34rpx;
        color: #001417;
        font-weight: bold;
        font-style: italic;
      }
    }
    .pt-head {
      background: #3b6373;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;
      text {
        font-size: 34rpx;
        color: #fff;
        font-weight: bold;
        font-style: italic;
      }
    }
    .yr-head {
      background: #67cbc0;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;
      text {
        font-size: 34rpx;
        color: #001417;
        font-weight: bold;
        font-style: italic;
      }
    }
    .con {
      .flex();
      flex-wrap: wrap;
      margin-top: 20rpx;
      .item {
        width: 343rpx;
        box-sizing: border-box;
        border-radius: 15rpx;
        background-color: #fff;
        padding: 27rpx 23rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
        .time {
          .flex(center,start);
          font-size: 17rpx;
          height: 38rpx;
          border-radius: 17rpx;
          color: #0d545d;
          background-color: #c6fced;
          .key {
            margin-left: 18rpx;
          }
          .countdown {
            font-size: 17rpx !important;
            color: #0d545d !important;
            font-weight: bold;
            line-height: 1 !important;
          }
        }
        .time.ms {
          background-color: #3b6373;
          border-radius: 0;
          color: #fff;
          .countdown {
            color: #fff !important;
          }
        }
        .img {
          position: relative;
          width: 298rpx;
          height: 298rpx;
          margin-top: 8rpx;
          image {
            width: 100%;
            height: 100%;
          }
          .cover {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            position: absolute;
            left: 0;
            top: 0;
            .flex(center,center);
            image {
              width: 260rpx;
              height: 177rpx;
            }
          }
        }
        .num {
          display: flex;
          align-items: flex-end;
          margin-top: 20rpx;
          .xsqg {
            font-size: 21rpx;
            color: #0f5760;
            font-weight: bold;
            font-style: italic;
            line-height: 1;
          }
          image {
            width: 90rpx;
            height: 23rpx;
          }
          text {
            font-size: 15rpx;
            color: #030303;
            line-height: 1;
            margin-left: 10rpx;
          }
        }
        .num.ms {
          background-color: #f7f7f7;
          height: 38rpx;
          margin-top: 5rpx;
          image {
            width: 109rpx;
            height: 38rpx;
          }
          text {
            color: #0f5760;
            margin-left: 3rpx;
            font-size: 15rpx;
            font-weight: bold;
            padding-bottom: 8rpx;
          }
        }
        .price {
          .flex(center,start);
          margin-top: 14rpx;
          .p1 {
            font-size: 22rpx;
            color: #0f5760;
            font-weight: bold;
          }
          .p2 {
            font-size: 15rpx;
            color: #0fba90;
            padding: 5rpx 10rpx;
            border-radius: 5rpx;
            border: 1rpx solid #0fba90;
            margin-left: 10rpx;
            line-height: 1;
          }
          .p3 {
            text-decoration: line-through;
            font-size: 17rpx;
            margin-left: 10rpx;
            color: #2f2d2d;
          }
          .right {
            display: flex;
            align-items: center;
            text {
              font-size: 17rpx;
              color: #0f5760;
              font-weight: bold;
            }
            image {
              width: 16rpx;
              height: 16rpx;
              margin-left: 4rpx;
            }
          }
        }
        .price.ms {
          .flex();
          .left {
            .flex(center,start);
          }
        }
        .info {
          margin-top: 13rpx;
          .name {
            font-size: 15rpx;
            color: #2f2d2d;
            font-weight: bold;
            .ellipsis();
          }
          .des {
            font-size: 15rpx;
            color: #2f2d2d;
            .ellipsis();
          }
        }
        .btns {
          .flex();
          margin-top: 8rpx;
          .left {
            position: relative;
            width: 87rpx;
            height: 12rpx;
            background: #ffffff;
            border: 2rpx solid #48edc4;
            border-radius: 6rpx;
            overflow: hidden;
            .bar {
              position: absolute;
              left: 0;
              top: 0;
              height: 12rpx;
              background: #48edc4;
              border-radius: 4rpx;
            }
          }
          .right {
            .flex(center,start);
            button {
              .flex(center,center);
              position: relative;
              width: 99rpx;
              height: 31rpx;
              text-align: center;
              color: #fff;
              padding: 0;
              margin-left: 5rpx;
              background-color: transparent;
              &::after {
                border: none;
              }
              image {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
              }
              text {
                position: relative;
                color: #fff;
                z-index: 9;
                font-size: 16rpx;
                font-weight: bold;
              }
            }
          }
        }
        .btns.bptj {
          justify-content: flex-end;
        }
        .btns.ms {
          .left {
            border-color: #3b6373;
            .bar {
              background-color: #3b6373;
            }
          }
        }
        .btns.yr {
          .left {
            border-color: #4c4c4c;
            .bar {
              background-color: #4c4c4c;
            }
          }
          .right {
            button {
              position: relative;
              image {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
              }
              text {
                position: relative;
                z-index: 9;
                color: #fff;
                font-size: 16rpx;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    .more {
      width: 166rpx;
      line-height: 48rpx;
      text-align: center;
      background: #ffffff;
      border-radius: 10rpx;
      margin: 20rpx auto 0;
      font-size: 26rpx;
      color: #0f5760;
    }
  }

  .list {
    margin-top: 20rpx;
    .head {
      .flex(center,center);
      height: 66rpx;
      background: #c6fced;
      box-shadow: 0rpx 8rpx 13rpx 0rpx rgba(3, 48, 60, 0.14);
      border-radius: 15rpx;
      text {
        font-size: 34rpx;
        color: #001417;
        font-weight: bold;
        font-style: italic;
      }
    }
    .con {
      .flex();
      flex-wrap: wrap;
      margin-top: 20rpx;
      .list-item {
        position: relative;
        width: 343rpx;
        height: 298rpx;
        box-sizing: border-box;
        border-radius: 15rpx;
        background-color: #fff;
        margin-bottom: 20rpx;
        box-shadow: 0 8rpx 13rpx 0 rgba(3, 48, 60, 0.14);
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
        .name {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 60rpx;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          font-size: 24rpx;
          font-weight: bold;
          .flex(center,center);
        }
      }
    }
    .more {
      width: 166rpx;
      line-height: 48rpx;
      text-align: center;
      background: #ffffff;
      border-radius: 10rpx;
      margin: 20rpx auto 0;
      font-size: 26rpx;
      color: #0f5760;
    }
  }
}

/* 新闻弹窗样式 */
.news-body {
  padding: 20rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

/* 回到顶部按钮 */
.top {
  position: fixed;
  right: 30rpx;
  bottom: 200rpx;
  width: 80rpx;
  height: 80rpx;
  z-index: 99;
}

