import dayjs from './dayjs'
export default () => {
  const startY = 2021
  const currY = dayjs().year()
  const currM = dayjs().month() + 1
  let arr = [{ text: '近三个月', value: `${dayjs().subtract(2, 'M').startOf('M').format('YYYY-MM-DD')}~${dayjs().endOf('M').format('YYYY-MM-DD')}` }]
  for (let y = currY; y >= startY; y--) {
    if (y === currY) {
      for (let m = currM; m >= 1; m--) {
        const format = `${y}-${m}`
        arr.push({
          text: `${y}年${m}月`,
          value: `${dayjs(format).startOf('M').format('YYYY-MM-DD')}~${dayjs(format).endOf('M').format('YYYY-MM-DD')}`
        })
      }
    } else {
      for (let m = 12; m >= 1; m--) {
        const format = `${y}-${m}`
        arr.push({
          text: `${y}年${m}月`,
          value: `${dayjs(format).startOf('M').format('YYYY-MM-DD')}~${dayjs(format).endOf('M').format('YYYY-MM-DD')}`
        })
      }
    }
  }
  return arr
}
