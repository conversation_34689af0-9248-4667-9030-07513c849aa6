<style lang="less">
@import '../../common/common.less';
.list {
  padding-top: 176rpx;
  .list-item {
    display: flex;
    padding: 17rpx 33rpx;
    padding-bottom: 30rpx;
    box-sizing: border-box;
    margin: 30rpx 25rpx 0;
    background-color: #fff;
    box-shadow: 0 4rpx 12rpx 1rpx rgba(3, 3, 3, 0.06);
    border-radius: 20rpx;
    .left {
      width: 200rpx;
      height: 200rpx;
      border-radius: 20rpx;
    }
    .right {
      flex: 1;
      min-height: 200rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 20rpx;
      .name {
        font-size: 32rpx;
        color: #393737;
        font-weight: bold;
        .ellipsis();
      }
      .des {
        font-size: 32rpx;
        color: #393737;
        margin-top: 12rpx;
      }
      .price-row1 {
        .flex(center,space-between);
        .vip {
          .flex(center, start);
          flex-wrap: wrap;
          &-val {
            font-size: 28rpx;
            font-weight: bold;
            color: #129799;
          }
          .tag {
            line-height: 34rpx;
            text-align: center;
            background: #a8f2ed;
            padding: 0 10rpx;
            border-radius: 8rpx;
            font-size: 20rpx;
            color: #052829;
            margin-right: 13rpx;
          }
        }
        .normal {
          .flex(center, start);
          flex-wrap: wrap;
          &-val {
            text-decoration: line-through;
          }
          .tag {
            line-height: 34rpx;
            text-align: center;
            border: 2rpx solid #afafaf;
            padding: 0 10rpx;
            border-radius: 8rpx;
            font-size: 20rpx;
            margin-right: 13rpx;
            box-sizing: border-box;
          }
        }
      }
      .price-row2 {
        .flex(center,space-between);
        .earnest {
          .flex(center, center);
          .tag {
            font-size: 19rpx;
            padding: 0 10rpx;
            line-height: 35rpx;
            text-align: center;
            border-radius: 8rpx;
            color: #fff;
            margin-right: 10rpx;
            background-color: #428485;
          }
          .price {
            font-size: 28rpx;
            font-weight: bold;
            color: #242d2b;
          }
        }
        .cart {
          width: 50rpx;
          height: 50rpx;
        }
      }
      &:not(:last-child) {
        padding-bottom: 30rpx;
        border-bottom: 1rpx solid #eee;
      }
    }
  }
}
</style>
<template>
  <empty v-if="loadType === 'done' && productList.length === 0" />
  <div class="list">
    <block v-for="(item, i) in productList">
      <proItem :data="item" :key="i" @tap="goDetail(item)" customStyle="margin: 30rpx 25rpx 0" />
    </block>
  </div>
  <loadMoreBottom
    v-if="loadType === 'loading' || productList.length > 0"
    :type="loadType"
    @refresh="getProductList"
  />
</template>
<script>
import wepy from '@wepy/core'
import { FeaturedProduct, FeaturedService } from '@s'

const apiMap = {
  FeaturedProduct, // 报单爆品商品
  FeaturedService // 报单爆品服务
}

wepy.component({
  props: {
    api: String
  },
  data: {
    completeFetch: false, // 数据是否完全获取的标志
    productList: [], // 商品列表
    loadType: 'loading', // 当前加载状态
    page: 1, // 当前页码
    pageSize: 10, // 每页显示的商品数量
    total: 0 // 商品总数
  },
  methods: {
    getProductList() {
      const { page, pageSize, productList } = this
      const data = { Country: 'CN', Page: page, PageSize: pageSize }
      this.loadType = 'loading' // 设置加载状态为加载中
      apiMap[this.api](data)
        .then(res => {
          const d = res.data.d
          if (d && Array.isArray(d)) {
            // 更新商品列表
            this.productList = page === 1 ? d : [...productList, ...d]
            this.total = d[0]?.TotalProduct || 0 // 获取总商品数
            this.loadType = this.productList.length >= this.total ? 'done' : 'loading' // 更新加载状态
            if (this.loadType === 'loading') {
              this.page += 1 // 如果还在加载中，增加页码
            }
          } else {
            this.loadType = 'error' // 如果没有数据，设置为错误状态
          }
        })
        .catch(() => {
          this.loadType = 'error' // 捕获错误，设置为错误状态
        })
        .finally(() => {
          this.completeFetch = true // 数据获取完成
          wx.hideLoading() // 隐藏加载提示
          wx.stopPullDownRefresh() // 停止下拉刷新
        })
    },
    // 下拉刷新
    onRefresh() {
      this.page = 1 // 重置页码
      this.getProductList() // 重新获取列表
    },
    // 加载下一页
    onLoadMore() {
      if (this.loadType !== 'done') {
        this.getProductList() // 如果没有加载完成，继续获取列表
      }
    },
    // 导航到商品详情页面
    navigateToDetail(url) {
      wx.navigateTo({ url })
    },
    // 处理商品详情的跳转逻辑
    goDetail(data) {
      const {
        ProductCode,
        PreGroupStartDate,
        PreLimitedStartDate,
        PreGroupReferNo,
        PreLimitedReferNo
      } = data
      const now = +new Date() // 当前时间戳
      const isService = ['FeaturedService'].includes(this.api)
      // 判断是否在团购开始前
      if (
        PreGroupStartDate &&
        PreGroupStartDate !== '-' &&
        +new Date(PreGroupStartDate.replaceAll('-', '/')) > now
      ) {
        this.navigateToDetail(
          `/pages/proDetail?ProductCode=${ProductCode}&PromoType=GP&type=pre&ReferNo=${PreGroupReferNo}&isService=${isService}`
        )
      } else if (
        PreLimitedStartDate &&
        PreLimitedStartDate !== '-' &&
        +new Date(PreLimitedStartDate.replaceAll('-', '/')) > now
      ) {
        this.navigateToDetail(
          `/pages/proDetail?ProductCode=${ProductCode}&PromoType=LT&type=pre&ReferNo=${PreLimitedReferNo}&isService=${isService}`
        )
      } else {
        this.navigateToDetail(`/pages/proDetail?ProductCode=${ProductCode}&isService=${isService}`)
      }
    }
  }
})
</script>
<config>
{
    usingComponents: {
        "loadMoreBottom": "~@/components/loadMoreBottom",
        "proItem": "~@/components/productList/item",
        "empty": "~@/components/empty"
    }
}
</config>
