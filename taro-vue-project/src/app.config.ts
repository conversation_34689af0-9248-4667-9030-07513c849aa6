// 应用配置文件 - 从WePY2迁移到Taro
export default defineAppConfig({
  // 页面路径列表 - 从原WePY项目迁移（暂时只保留首页进行测试）
  pages: [
    'pages/index/index'
    // 其他页面待后续迁移
    // 'pages/classification/index',
    // 'pages/homeCategory/index',
    // 'pages/cart/index',
    // 'pages/my/index',
    // 'pages/recommend/index',
    // 'pages/search/index',
    // 'pages/proDetail/index',
    // 'pages/submitOrder/index',
    // 'pages/payPsw/index',
    // 'pages/order/index',
    // 'pages/orderDetail/index',
    // 'pages/pay/index',
    // 'pages/groupDetail/index',
    // 'pages/proComment/index',
    // 'pages/comment/index',
    // 'pages/deliveDetail/index',
    // 'pages/afterSale/index',
    // 'pages/submitAfterSale/index',
    // 'pages/afterSaleDetail/index',
    // 'pages/myFans/index',
    // 'pages/myWallet/index',
    // 'pages/eWallet/index',
    // 'pages/sWallet/index',
    // 'pages/fWallet/index',
    // 'pages/ptOrderSuccess/index',
    // 'pages/ctOrderSuccess/index',
    // 'pages/sharePtDetail/index'
  ],

  // 分包配置 - 从原WePY项目迁移（暂时注释，等页面迁移完成后再启用）
  // subpackages: [
  //   {
  //     root: 'subpackages/pages',
  //     pages: [
  //       'login2/index',
  //       'shareRegister/index',
  //       'editUserinfo/index',
  //       'address/index',
  //       'editAddress/index',
  //       'searchPro/index',
  //       'freePro/index',
  //       'package/index',
  //       'vipPackage/index',
  //       'discount/index',
  //       'limitTime/index',
  //       'groupBuy/index',
  //       'preLimitTime/index',
  //       'preGroupBuy/index',
  //       'preAll/index',
  //       'income/index',
  //       'shareIncome/index',
  //       'thankfulIncome/index',
  //       'promotionIncome/index',
  //       'groupIncome/index',
  //       'redpackIncome/index',
  //       'live/index',
  //       'withdraw/index',
  //       'settings/index',
  //       'changePsw/index',
  //       'myKyc/index',
  //       'kyc/index',
  //       'resetPsw/index',
  //       'protocol/index',
  //       'qrcode/index',
  //       'store/index',
  //       'joinVenture/index',
  //       'cityAgency/index',
  //       'orgCooperation/index',
  //       'docList/index',
  //       'docDetail/index',
  //       'mainCategory/index',
  //       'brandDiscount/index',
  //       'featuredList/index',
  //       'bank/index',
  //       'editBank/index',
  //       'tml/index'
  //     ]
  //   },
  //   {
  //     root: 'income/pages',
  //     pages: [
  //       'baopinIncome/index',
  //       'hongbaoIncome/index',
  //       'zhizunjiayouIncome/index',
  //       'gongsifenxiangIncome/index',
  //       'gongsifanliIncome/index',
  //       'lianchuangfenchengIncome/index',
  //       'fuwushangIncome/index',
  //       'gouwufankuan/index',
  //       'shareOrgIncome/index'
  //     ]
  //   }
  // ],

  // TabBar配置 - 从原WePY项目迁移（暂时注释，等静态资源准备好后再启用）
  // tabBar: {
  //   color: '#171717',
  //   selectedColor: '#549795',
  //   borderStyle: 'black',
  //   list: [
  //     {
  //       pagePath: 'pages/index/index',
  //       text: '首页',
  //       selectedIconPath: 'static/tabBar/tab1-selected.png',
  //       iconPath: 'static/tabBar/tab1.png'
  //     },
  //     {
  //       pagePath: 'pages/classification/index',
  //       text: '分类',
  //       selectedIconPath: 'static/tabBar/tab2-selected.png',
  //       iconPath: 'static/tabBar/tab2.png'
  //     },
  //     {
  //       pagePath: 'pages/cart/index',
  //       text: '购物车',
  //       selectedIconPath: 'static/tabBar/tab3-selected.png',
  //       iconPath: 'static/tabBar/tab3.png'
  //     },
  //     {
  //       pagePath: 'pages/my/index',
  //       text: '我的',
  //       selectedIconPath: 'static/tabBar/tab4-selected.png',
  //       iconPath: 'static/tabBar/tab4.png'
  //     }
  //   ]
  // },

  // 全局窗口配置 - 从原WePY项目迁移并适配
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#8CDAC6', // 保持原项目的主题色
    navigationBarTitleText: '安免1号',
    navigationBarTextStyle: 'white',
    backgroundColor: '#f6f6f6'
  },

  // 权限配置 - 从原WePY项目迁移
  permission: {
    'scope.userFuzzyLocation': {
      desc: '你的位置信息将用于小程序位置接口的效果展示'
    }
  },

  // 隐私设置 - 从原WePY项目迁移
  requiredPrivateInfos: [
    'chooseAddress',
    'getFuzzyLocation'
  ]
})
