/**
* 图片上传七牛云
*/
import { QiniuToken } from '@s'
const qiniuHost = 'https://upload-z2.qiniup.com'
export default (tempFilePaths, token) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: qiniuHost,
      name: 'file',
      filePath: tempFilePaths[0],
      header: {
        'Content-Type': 'multipart/form-data'
      },
      formData: {
        token,
        key: tempFilePaths[0].split('tmp/').pop()
      },
      success: function (res) {
        let data = JSON.parse(res.data)
        if (data.error) {
          reject(data)
        } else {
          resolve(data)
        }
      },
      fail: function (res) {
        reject(res)
      }
    })
  })
}
const getQiniuToken = (FileName) => {
  return new Promise(resolve => {
    QiniuToken({ FileName }).then(res => {
      resolve(res.data.d[0])
    })
  })
}
export { qiniuHost, getQiniuToken }
