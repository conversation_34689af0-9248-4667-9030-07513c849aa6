import QQMapWX from './qqmap-wx-jssdk.min'
const qqmapsdk = new QQMapWX({
  key: 'TFOBZ-4CWKQ-SGZ5Y-2AEO2-5QSYK-YNBCU'
})

// 限制充值金额只能输入小数点后2位
const limitCash = val => {
  let num = val.toString() // 先转换成字符串类型
  if (num.indexOf('.') === 0) {
    // 第一位就是 .
    num = 0 + num
  }
  num = num.replace(/[^\d.]/g, '') // 清除“数字”和“.”以外的字符
  num = num.replace(/\.{2,}/g, '.') // 只保留第一个. 清除多余的
  num = num.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
  num = num.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
  if (num.indexOf('.') < 0 && num !== '') {
    num = parseFloat(num)
  }
  return num
}

// 检查位置权限状态
const checkLocationAuth = async () => {
  try {
    const setting = await wx.getSetting()
    if (setting.authSetting['scope.userFuzzyLocation'] === false) {
      // 用户曾经拒绝过授权，显示重新授权对话框
      return new Promise((resolve, reject) => {
        wx.showModal({
          title: '需要位置权限',
          content: '为了提供更好的服务，需要获取您的位置信息，是否前往设置打开？',
          confirmText: '前往设置',
          cancelText: '暂不开启',
          success: res => {
            if (res.confirm) {
              // 打开设置页面
              wx.openSetting({
                success: settingRes => {
                  if (settingRes.authSetting['scope.userFuzzyLocation']) {
                    resolve()
                  } else {
                    reject(new Error('用户未授权位置信息'))
                  }
                },
                fail: reject
              })
            } else {
              reject(new Error('用户取消授权'))
            }
          },
          fail: reject
        })
      })
    }
    return Promise.resolve()
  } catch (error) {
    return Promise.reject(error)
  }
}

// 获取位置信息
const getLocation = async () => {
  try {
    // 先检查权限
    await checkLocationAuth()

    // 获取位置信息
    const location = await new Promise((resolve, reject) => {
      wx.getFuzzyLocation({
        type: 'wgs84',
        success: resolve,
        fail: error => {
          if (error.errMsg.includes('auth deny')) {
            // 用户在系统弹窗中拒绝授权
            checkLocationAuth()
              .then(() => getLocation())
              .catch(reject)
          } else {
            reject(error)
          }
        }
      })
    })

    // 调用腾讯地图逆地址解析
    return new Promise((resolve, reject) => {
      qqmapsdk.reverseGeocoder({
        location: {
          latitude: location.latitude,
          longitude: location.longitude
        },
        success: res => {
          const address = res.result.address_component
          console.log('腾讯地图返回地址信息：', address)
          resolve({
            State: address.province,
            City: address.city,
            District: address.district
          })
        },
        fail: error => {
          console.error('逆地址解析失败：', error)
          wx.showToast({
            title: '获取地址信息失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  } catch (error) {
    console.error('获取位置失败：', error)
    wx.showToast({
      title: error.message || '获取位置失败',
      icon: 'none'
    })
  }
}

export { limitCash, checkLocationAuth, getLocation }
