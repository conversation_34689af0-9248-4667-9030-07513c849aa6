<style lang="less">
@import '../../common/common.less';
.container {
  padding: 35rpx;
  border-radius: 20rpx;
  background-color: #fff;
}
.swiper {
  height: 430rpx;
}
.top {
  .flex();
  .right {
    .flex(center,start);
    font-size: 28rpx;
    color: #7b7b7b;
    .icon {
      font-size: 30rpx;
      color: #7b7b7b;
      transform: rotate(90deg);
      margin-right: 10rpx;
    }
  }
}
.list {
  .flex(center,start);
  margin-top: 30rpx;
  .item {
    width: 188rpx;
    &:not(:nth-child(3n)) {
      margin-right: 30rpx;
    }
    image {
      width: 100%;
      height: 189rpx;
      border: 2px solid #e5e5e5;
      border-radius: 10px;
    }
    .name {
      font-size: 24rpx;
      color: #4b4b4b;
      height: 53rpx;
      line-height: 28rpx;
      .ellipsis(2);
      margin-top: 16rpx;
    }
    .price {
      font-size: 38rpx;
      color: #9f2304;
      font-weight: bold;
      margin-top: 7rpx;
      label {
        font-size: 28rpx;
      }
    }
  }
}
</style>
<template>
  <div class="container" v-if="list.length > 0">
    <div class="top">
      <div class="title">
        <img
          src="/static/recommend/xianshiqianggou.png"
          style="width: 162rpx; height: 36rpx"
          v-if="title === '限时抢购'"
        />
        <img
          src="/static/recommend/pintuanmiaosha.png"
          style="width: 211rpx; height: 49rpx"
          v-if="title === '拼团秒杀'"
        />
        <img
          src="/static/recommend/yurexinpin.png"
          style="width: 242rpx; height: 50rpx"
          v-if="title === '预热新品'"
        />
        <img
          src="/static/recommend/tuiijanhaohuo.png"
          style="width: 152rpx; height: 37rpx"
          v-if="title === '推荐好货'"
        />
      </div>
      <div class="right">
        <van-icon name="down" class="icon" />
        滑动更多
      </div>
    </div>
    <swiper
      class="swiper"
      indicator-color="#E0E0E0"
      indicator-active-color="#9F9F9F"
      indicator-dots="{{true}}"
    >
      <swiper-item v-for="(item, i) in list" :key="i">
        <div class="list">
          <div class="item" v-for="(item2, i2) in item" :key="i2" @tap="goDetail(item2)">
            <img :src="item2.Image" alt="" />
            <div class="name">{{ item2.Title || item2.Name }}</div>
            <div class="price"><span>¥</span>{{ item2.MemberPrice }}</div>
          </div>
        </div>
      </swiper-item>
    </swiper>
  </div>
</template>
<script>
import wepy from '@wepy/core'
import chunk from 'lodash.chunk'
import { LimitedTimeProduct, GroupPurchaseProduct, RecommendedProduct, PreProductList } from '@s'
wepy.component({
  props: {
    type: {
      type: String,
      default: 'qg'
    }
  },
  data: {
    list: [],
    title: ''
  },
  methods: {
    goDetail(data) {
      const { type } = this
      let url = ''
      switch (type) {
        case 'qg':
          url = `/pages/proDetail?ProductCode=${data.ProductCode}&isService=${
            data.Product === 'False'
          }`
          break
        case 'pt':
          url = `/pages/proDetail?ProductCode=${data.ProductCode}&isService=${
            data.Product === 'False'
          }`
          break
        case 'yr':
          url = `/pages/proDetail?ProductCode=${data.ProductCode}&isService=${
            data.Product === 'False'
          }&type=pre&PromoType=${data.PromoType}&ReferNo=${data.ReferNo}`
          break
        case 'tj':
          url = `/pages/proDetail?ProductCode=${data.ProductCode}&isService=${
            data.Product === 'False'
          }&FreeProduct=2`
          break
      }
      wx.navigateTo({ url })
    }
  },
  attached() {
    const { type } = this
    switch (type) {
      case 'qg':
        this.title = '限时抢购'
        LimitedTimeProduct({ Page: 1, PageSize: 9 }).then(res => {
          this.list = chunk(res.data.d, 3)
        })
        break
      case 'pt':
        this.title = '拼团秒杀'
        GroupPurchaseProduct({ Page: 1, PageSize: 9 }).then(res => {
          this.list = chunk(res.data.d, 3)
        })
        break
      case 'yr':
        this.title = '预热新品'
        PreProductList({ Page: 1, PageSize: 9 }).then(res => {
          this.list = chunk(res.data.d, 3)
        })
        break
      case 'tj':
        this.title = '推荐好货'
        RecommendedProduct({ Page: 1, PageSize: 9 }).then(res => {
          this.list = chunk(res.data.d, 3)
        })
        break
    }
  }
})
</script>
<config>
{
    usingComponents: {
      'van-icon': '~vant/icon/index',
    }
}
</config>
