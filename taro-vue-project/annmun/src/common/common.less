@primary-color: #8cdac6;
@btn-color: #117567;
@btn-border-color: #b93848;

.flex(@item: center, @content: space-between) {
  display: flex;
  align-items: @item;
  justify-content: @content;
}

.ellipsis(@line: 1) {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: @line;
  -webkit-box-orient: vertical;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
}

.hover {
  opacity: 0.6;
}

.btn1-bg {
  background: linear-gradient(to bottom, #1ed5ba, #0d5d51);
  border-radius: 8rpx;
}

.btn2-bg {
  background: linear-gradient(to bottom, #398DB2, #1E4A5E);
  border-radius: 8rpx;
  color: #fff;
}

.btn3-bg {
  background: linear-gradient(to bottom, #A4E7E1, #68D7CD);
  border-radius: 8rpx;
  color: #273d36;
}